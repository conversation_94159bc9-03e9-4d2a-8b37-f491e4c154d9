#!/usr/bin/env python3
"""
Options AI Training Agent - Machine Learning Model Development

Features:
📊 1. Multi-Model Training
- LightGBM for gradient boosting
- SGD for incremental learning
- Random Forest for baseline

📈 2. Options-Specific Features
- Greeks-based feature engineering
- Volatility surface modeling
- Time decay prediction
- Multi-timeframe analysis

⚡ 3. Advanced Training
- Hyperparameter optimization
- Cross-validation strategies
- Feature importance analysis
- Model ensemble techniques

🎯 4. Performance Optimization
- GPU acceleration support
- Parallel training pipelines
- Memory-efficient processing
- Real-time model updates

🔄 5. Incremental Learning Support
- Online learning with partial_fit
- Concept drift detection
- Model versioning and rollback
- Continuous model updates
"""

import asyncio
import logging
import polars as pl
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import json
import joblib
from dataclasses import dataclass
import warnings
import hashlib
import os
warnings.filterwarnings('ignore')

# Core ML libraries
import lightgbm as lgb
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import SGDRegressor, PassiveAggressiveRegressor
from sklearn.tree import DecisionTreeRegressor
import optuna

logger = logging.getLogger(__name__)

class OptionsAITrainingAgent:
    """
    Options AI Training Agent for ML model development
    
    Handles:
    - Options price prediction models
    - Strategy performance prediction
    - Risk assessment models
    - Model ensemble creation
    - Incremental learning support
    """
    
    def __init__(self, config_path: str = "config/options_ai_training_config.yaml"):
        """Initialize Options AI Training Agent"""
        self.config_path = config_path
        self.config = None
        self.is_running = False
        
        # Data paths
        self.data_path = Path("data")
        self.features_path = self.data_path / "features"
        self.models_path = self.data_path / "models"
        self.backtest_path = self.data_path / "backtest"
        self.ai_training_path = self.data_path / "ai_training"
        
        # Create directories
        self.models_path.mkdir(parents=True, exist_ok=True)
        self.ai_training_path.mkdir(parents=True, exist_ok=True)
        
        # Model storage
        self.trained_models = {}
        
        # Incremental learning support
        self.incremental_models = {}
        self.model_versions = {}
        
        # Data tracking for duplicate prevention
        self.training_registry_path = self.ai_training_path / "training_data_registry.json"
        self.training_registry = None
        
        logger.info("[INIT] Options AI Training Agent initialized")
    
    async def initialize(self, **kwargs):
        """Initialize the agent with optional parameters"""
        try:
            await self._load_config()
            
            # Store kwargs for later use
            self.init_kwargs = kwargs
            
            # Check for incremental training flag
            self.incremental_mode = kwargs.get('incremental', True)  # Default to incremental
            
            # Load training data registry
            await self._load_training_registry()
            
            logger.info(f"[SUCCESS] Options AI Training Agent initialized successfully (incremental: {self.incremental_mode})")
            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize agent: {e}")
            return False
    
    async def _load_config(self):
        """Load configuration with incremental training support"""
        self.config = {
            'target_variables': ['option_price', 'implied_vol', 'strategy_return', 'annualized_return', 'sharpe_ratio'],
            'feature_columns': [
                # Greeks features
                'delta', 'gamma', 'theta', 'vega', 'iv_rank',
                # Index reference features
                'index_price', 'index_return_1min', 'index_return_3min', 'index_return_5min', 'index_return_15min',
                'index_volatility', 'index_volatility_10', 'index_volatility_50',
                'index_momentum_10', 'index_momentum_20', 'index_above_sma20', 'index_above_sma50',
                # Options-specific features
                'moneyness', 'moneyness_deviation', 'distance_from_atm', 'is_itm', 'time_to_expiry',
                # Combined features
                'delta_exposure', 'delta_pnl_1min', 'gamma_risk', 'vega_exposure', 'theta_decay_rate',
                # PE/CE analysis features
                'is_pe', 'is_ce', 'direction_alignment',
                # Technical features (timeframe-dependent)
                'rsi', 'sma_20', 'ema_20', 'volume_ratio', 'momentum'
            ],
            'model_types': ['lightgbm', 'sgd'],  # Added SGD for incremental learning
            'cv_folds': 3,
            'optuna_trials': 10,
            'optuna_timeout': 60,
            'use_index_reference': True,
            'pe_ce_filter': True,
            'timeframes': ['1min', '3min', '5min', '15min'],
            'train_ensemble': True,
            'parallel_training': True,
            'max_workers': 4,
            # Incremental learning settings
            'incremental_batch_size': 1000,
            'concept_drift_threshold': 0.1,
            'model_performance_window': 100,
            'incremental_models': ['sgd', 'passive_aggressive']
        }
    
    async def _load_training_registry(self):
        """Load or create training data registry"""
        try:
            if self.training_registry_path.exists():
                with open(self.training_registry_path, 'r') as f:
                    self.training_registry = json.load(f)
                logger.info("[REGISTRY] Loaded existing training data registry")
            else:
                # Create new registry
                self.training_registry = {
                    "version": "1.0",
                    "description": "Registry to track processed training data and avoid duplicates",
                    "last_updated": None,
                    "processed_data_hashes": {},
                    "training_sessions": {},
                    "data_sources": {
                        "features": {},
                        "backtest": {},
                        "strategies": {}
                    },
                    "incremental_checkpoints": {}
                }
                await self._save_training_registry()
                logger.info("[REGISTRY] Created new training data registry")
        except Exception as e:
            logger.error(f"[ERROR] Failed to load training registry: {e}")
            # Create empty registry as fallback
            self.training_registry = {
                "version": "1.0",
                "processed_data_hashes": {},
                "training_sessions": {},
                "data_sources": {"features": {}, "backtest": {}, "strategies": {}},
                "incremental_checkpoints": {}
            }
    
    async def _save_training_registry(self):
        """Save training data registry"""
        try:
            self.training_registry["last_updated"] = datetime.now().isoformat()
            with open(self.training_registry_path, 'w') as f:
                json.dump(self.training_registry, f, indent=2)
            logger.info("[REGISTRY] Training data registry saved")
        except Exception as e:
            logger.error(f"[ERROR] Failed to save training registry: {e}")
    
    def _calculate_data_hash(self, data: pl.DataFrame) -> str:
        """Calculate hash of training data to detect duplicates"""
        try:
            # Create a hash based on data shape, columns, and sample of data
            data_info = {
                'shape': data.shape,
                'columns': sorted(data.columns),
                'dtypes': [str(dtype) for dtype in data.dtypes]
            }
            
            # Add sample of data for more robust hashing
            if data.height > 0:
                sample_size = min(100, data.height)
                sample_data = data.sample(n=sample_size, seed=42)
                data_info['sample_hash'] = hashlib.md5(
                    str(sample_data.to_pandas().values.tobytes()).encode()
                ).hexdigest()
            
            # Create final hash
            data_str = json.dumps(data_info, sort_keys=True)
            return hashlib.sha256(data_str.encode()).hexdigest()
        except Exception as e:
            logger.warning(f"[WARNING] Failed to calculate data hash: {e}")
            return f"fallback_{datetime.now().timestamp()}"
    
    def _is_data_already_processed(self, data_hash: str, data_source: str) -> bool:
        """Check if data has already been processed"""
        return data_hash in self.training_registry.get("processed_data_hashes", {})
    
    def _mark_data_as_processed(self, data_hash: str, data_source: str, metadata: Dict[str, Any]):
        """Mark data as processed in registry"""
        if "processed_data_hashes" not in self.training_registry:
            self.training_registry["processed_data_hashes"] = {}
        
        self.training_registry["processed_data_hashes"][data_hash] = {
            "source": data_source,
            "processed_at": datetime.now().isoformat(),
            "metadata": metadata
        }
    
    async def start(self, **kwargs) -> bool:
        """Start the AI training agent with incremental learning support"""
        try:
            logger.info("[START] Starting Options AI Training Agent...")
            
            # Check if incremental mode is enabled
            incremental = kwargs.get('incremental', False) or self.incremental_mode
            
            if incremental:
                logger.info("[INCREMENTAL] Starting incremental training mode...")
                return await self._start_incremental_training(**kwargs)
            else:
                logger.info("[BATCH] Starting batch training mode...")
                return await self._start_batch_training(**kwargs)
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to start agent: {e}")
            return False
    
    async def _start_incremental_training(self, **kwargs) -> bool:
        """Start incremental training workflow with duplicate data filtering"""
        try:
            logger.info("[INCREMENTAL] Starting incremental training with duplicate filtering...")
            
            # Create training session ID
            session_id = f"training_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Load existing models if available
            await self._load_existing_models()
            
            # Load and filter training data
            training_data = await self._load_and_filter_training_data()
            if training_data is None or training_data.height == 0:
                logger.info("[INCREMENTAL] No new data to process (all data already processed)")
                return True
            
            logger.info(f"[INCREMENTAL] Found {training_data.height} new records for training")
            
            # Record training session
            self.training_registry["training_sessions"][session_id] = {
                "started_at": datetime.now().isoformat(),
                "data_records": training_data.height,
                "status": "in_progress"
            }
            
            # Process data in batches for incremental learning
            batch_size = self.config['incremental_batch_size']
            total_rows = training_data.height
            processed_batches = 0
            
            logger.info(f"[INCREMENTAL] Processing {total_rows} records in batches of {batch_size}")
            
            for i in range(0, total_rows, batch_size):
                batch_end = min(i + batch_size, total_rows)
                batch_data = training_data.slice(i, batch_end - i)
                
                logger.info(f"[INCREMENTAL] Processing batch {i//batch_size + 1}/{(total_rows + batch_size - 1)//batch_size}")
                
                # Calculate batch hash and check if already processed
                batch_hash = self._calculate_data_hash(batch_data)
                
                if not self._is_data_already_processed(batch_hash, "incremental_batch"):
                    # Update models incrementally
                    await self._update_models_incrementally(batch_data)
                    
                    # Mark batch as processed
                    self._mark_data_as_processed(batch_hash, "incremental_batch", {
                        "session_id": session_id,
                        "batch_number": i//batch_size + 1,
                        "records": batch_data.height
                    })
                    
                    processed_batches += 1
                    
                    # Check for concept drift
                    if await self._detect_concept_drift(batch_data):
                        logger.warning("[DRIFT] Concept drift detected, retraining models...")
                        await self._retrain_models_on_drift(batch_data)
                else:
                    logger.info(f"[SKIP] Batch {i//batch_size + 1} already processed, skipping...")
            
            # Update training session
            self.training_registry["training_sessions"][session_id].update({
                "completed_at": datetime.now().isoformat(),
                "processed_batches": processed_batches,
                "status": "completed"
            })
            
            # Save updated models and registry
            await self._save_models()
            await self._save_training_registry()
            
            logger.info(f"[SUCCESS] Incremental training completed - processed {processed_batches} new batches")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Incremental training failed: {e}")
            # Update session status
            if 'session_id' in locals():
                self.training_registry["training_sessions"][session_id]["status"] = "failed"
                self.training_registry["training_sessions"][session_id]["error"] = str(e)
                await self._save_training_registry()
            return False
    
    async def _start_batch_training(self, **kwargs) -> bool:
        """Start traditional batch training workflow"""
        try:
            # Load training data
            training_data = await self._load_training_data()
            if training_data is None:
                return False
            
            # Train models for each target
            for target in self.config['target_variables']:
                if target not in training_data.columns:
                    continue
                    
                await self._train_target_models(training_data, target)
            
            # Save models
            await self._save_models()
            
            logger.info("[SUCCESS] Batch training completed")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Batch training failed: {e}")
            return False
    
    async def _load_training_data(self) -> Optional[pl.DataFrame]:
        """Load training data from feature engineering outputs"""
        try:
            logger.info("[LOAD] Loading training data...")

            # Load feature data from all timeframes
            timeframes = self.config['timeframes']
            underlyings = ['NIFTY', 'BANKNIFTY']
            all_training_data = []

            for timeframe in timeframes:
                timeframe_path = self.features_path / timeframe
                if not timeframe_path.exists():
                    continue

                for underlying in underlyings:
                    feature_files = list(timeframe_path.glob(f"{underlying}_{timeframe}_features_*.parquet"))
                    if feature_files:
                        latest_file = max(feature_files, key=lambda x: x.stat().st_mtime)
                        df = pl.read_parquet(latest_file)

                        # Filter for PE/CE options if enabled
                        if self.config['pe_ce_filter'] and 'option_type' in df.columns:
                            df = df.filter(
                                pl.col('option_type').is_not_null() &
                                pl.col('option_type').is_in(['PE', 'CE'])
                            )

                        if df.height > 0:
                            df = df.with_columns([pl.lit(timeframe).alias('timeframe')])
                            all_training_data.append(df)
                            logger.info(f"[LOAD] Loaded {df.height} records from {underlying} {timeframe}")

            if not all_training_data:
                logger.warning("[WARNING] No training data found")
                return None

            # Combine all data
            combined_data = pl.concat(all_training_data, how="diagonal")

            # Create target variables if missing
            combined_data = await self._create_target_variables(combined_data)

            logger.info(f"[SUCCESS] Loaded {combined_data.height} total training samples")
            return combined_data

        except Exception as e:
            logger.error(f"[ERROR] Failed to load training data: {e}")
            return None

    async def _create_target_variables(self, data: pl.DataFrame) -> pl.DataFrame:
        """Create target variables from available data"""
        try:
            # Create option_price target from close price
            if 'close' in data.columns and 'option_price' not in data.columns:
                data = data.with_columns([pl.col('close').alias('option_price')])

            # Create implied_vol target
            if 'implied_vol' not in data.columns:
                if 'close' in data.columns:
                    data = data.with_columns([
                        pl.col('close').pct_change().rolling_std(window_size=20).alias('implied_vol')
                    ])
                else:
                    data = data.with_columns([pl.lit(0.2).alias('implied_vol')])

            # Create strategy_return target
            if 'strategy_return' not in data.columns:
                if 'close' in data.columns:
                    data = data.with_columns([pl.col('close').pct_change().alias('strategy_return')])
                else:
                    data = data.with_columns([pl.lit(0.0).alias('strategy_return')])

            # Create annualized_return target
            if 'annualized_return' not in data.columns:
                data = data.with_columns([
                    (pl.col('strategy_return') * 252).alias('annualized_return')
                ])

            # Create sharpe_ratio target
            if 'sharpe_ratio' not in data.columns:
                data = data.with_columns([
                    (pl.col('strategy_return') / pl.col('implied_vol')).alias('sharpe_ratio')
                ])

            return data

        except Exception as e:
            logger.error(f"[ERROR] Failed to create target variables: {e}")
            return data

    async def _train_target_models(self, data: pl.DataFrame, target: str):
        """Train models for specific target"""
        try:
            logger.info(f"[TRAIN] Training models for target: {target}")

            if target not in data.columns:
                logger.warning(f"[WARNING] Target {target} not found in data")
                return

            # Feature selection
            feature_cols = [col for col in self.config['feature_columns'] if col in data.columns]
            if not feature_cols:
                logger.warning(f"[WARNING] No features found for target {target}")
                return

            # Convert to pandas for sklearn compatibility
            X_df = data.select(feature_cols).to_pandas()
            y_series = data.select([target]).to_pandas()[target]

            # Remove NaN values
            mask = ~(X_df.isna().any(axis=1) | y_series.isna())
            X = X_df[mask].values
            y = y_series[mask].values

            if len(X) == 0:
                logger.warning(f"[WARNING] No valid data for target {target}")
                return

            # Train models
            for model_type in self.config['model_types']:
                model_key = f"{target}_{model_type}"

                if model_type == 'lightgbm':
                    model = await self._train_lightgbm(X, y, model_key)
                elif model_type == 'sgd':
                    model = await self._train_sgd(X, y, model_key)

                if model:
                    self.trained_models[model_key] = {
                        'model': model,
                        'target': target,
                        'model_type': model_type,
                        'features': feature_cols
                    }
                    logger.info(f"[SUCCESS] Trained {model_key}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to train models for {target}: {e}")

    async def _train_lightgbm(self, X: np.ndarray, y: np.ndarray, model_key: str):
        """Train LightGBM model with Optuna optimization"""
        try:
            def objective(trial):
                params = {
                    'objective': 'regression',
                    'metric': 'rmse',
                    'boosting_type': 'gbdt',
                    'num_leaves': trial.suggest_int('num_leaves', 10, 300),
                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                    'feature_fraction': trial.suggest_float('feature_fraction', 0.4, 1.0),
                    'bagging_fraction': trial.suggest_float('bagging_fraction', 0.4, 1.0),
                    'bagging_freq': trial.suggest_int('bagging_freq', 1, 7),
                    'min_child_samples': trial.suggest_int('min_child_samples', 5, 100),
                    'verbosity': -1
                }

                # Time series cross-validation
                tscv = TimeSeriesSplit(n_splits=self.config['cv_folds'])
                scores = []

                for train_idx, val_idx in tscv.split(X):
                    X_train, X_val = X[train_idx], X[val_idx]
                    y_train, y_val = y[train_idx], y[val_idx]

                    train_data = lgb.Dataset(X_train, label=y_train)
                    val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)

                    model = lgb.train(
                        params,
                        train_data,
                        valid_sets=[val_data],
                        num_boost_round=100,  # Reduced for faster training
                        callbacks=[lgb.early_stopping(10), lgb.log_evaluation(0)]
                    )

                    y_pred = model.predict(X_val)
                    score = mean_squared_error(y_val, y_pred)
                    scores.append(score)

                return np.mean(scores)

            # Optimize hyperparameters
            study = optuna.create_study(direction='minimize')
            study.optimize(objective, n_trials=self.config['optuna_trials'], timeout=self.config['optuna_timeout'])

            # Train final model with best parameters
            best_params = study.best_params
            best_params.update({
                'objective': 'regression',
                'metric': 'rmse',
                'verbosity': -1
            })

            train_data = lgb.Dataset(X, label=y)
            final_model = lgb.train(best_params, train_data, num_boost_round=200)

            logger.info(f"[SUCCESS] LightGBM trained for {model_key}")
            return final_model

        except Exception as e:
            logger.error(f"[ERROR] LightGBM training failed for {model_key}: {e}")
            return None

    async def _train_sgd(self, X: np.ndarray, y: np.ndarray, model_key: str):
        """Train SGD model for incremental learning"""
        try:
            # Scale features for SGD
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)

            # Initialize SGD regressor
            model = SGDRegressor(
                loss='squared_error',
                learning_rate='adaptive',
                eta0=0.01,
                max_iter=1000,
                tol=1e-3,
                random_state=42
            )

            # Train model
            model.fit(X_scaled, y)

            # Store scaler with model
            model_with_scaler = {
                'model': model,
                'scaler': scaler
            }

            logger.info(f"[SUCCESS] SGD trained for {model_key}")
            return model_with_scaler

        except Exception as e:
            logger.error(f"[ERROR] SGD training failed for {model_key}: {e}")
            return None

    async def _load_existing_models(self):
        """Load existing models for incremental training"""
        try:
            model_files = list(self.models_path.glob("*.joblib"))
            for model_file in model_files:
                try:
                    model_data = joblib.load(model_file)
                    model_name = model_file.stem
                    self.trained_models[model_name] = model_data
                    logger.info(f"[LOAD] Loaded existing model: {model_name}")
                except Exception as e:
                    logger.warning(f"[WARNING] Failed to load model {model_file}: {e}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to load existing models: {e}")

    async def _update_models_incrementally(self, batch_data: pl.DataFrame):
        """Update models incrementally with new batch data"""
        try:
            for target in self.config['target_variables']:
                if target not in batch_data.columns:
                    continue

                # Get features
                feature_cols = [col for col in self.config['feature_columns'] if col in batch_data.columns]
                if not feature_cols:
                    continue

                # Prepare data
                X_df = batch_data.select(feature_cols).to_pandas()
                y_series = batch_data.select([target]).to_pandas()[target]

                mask = ~(X_df.isna().any(axis=1) | y_series.isna())
                X = X_df[mask].values
                y = y_series[mask].values

                if len(X) == 0:
                    continue

                # Update SGD models incrementally
                sgd_key = f"{target}_sgd"
                if sgd_key in self.trained_models:
                    model_data = self.trained_models[sgd_key]['model']
                    if isinstance(model_data, dict) and 'model' in model_data:
                        sgd_model = model_data['model']
                        scaler = model_data['scaler']

                        # Scale new data
                        X_scaled = scaler.transform(X)

                        # Partial fit
                        sgd_model.partial_fit(X_scaled, y)
                        logger.info(f"[INCREMENTAL] Updated {sgd_key} with {len(X)} samples")

        except Exception as e:
            logger.error(f"[ERROR] Failed to update models incrementally: {e}")

    async def _detect_concept_drift(self, batch_data: pl.DataFrame) -> bool:
        """Detect concept drift in new data"""
        try:
            # Simple concept drift detection based on performance degradation
            # In a real implementation, you would use more sophisticated methods
            return False  # Placeholder

        except Exception as e:
            logger.error(f"[ERROR] Failed to detect concept drift: {e}")
            return False

    async def _retrain_models_on_drift(self, batch_data: pl.DataFrame):
        """Retrain models when concept drift is detected"""
        try:
            logger.info("[DRIFT] Retraining models due to concept drift...")
            # Implement model retraining logic here

        except Exception as e:
            logger.error(f"[ERROR] Failed to retrain models on drift: {e}")

    async def _save_models(self):
        """Save trained models"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            for model_name, model_data in self.trained_models.items():
                model_file = self.models_path / f"{model_name}_{timestamp}.joblib"
                joblib.dump(model_data, model_file)
                logger.info(f"[SAVE] Saved model: {model_file}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to save models: {e}")

    async def cleanup(self):
        """Cleanup resources"""
        try:
            self.is_running = False
            logger.info("[CLEANUP] Options AI Training Agent cleaned up")
        except Exception as e:
            logger.error(f"[ERROR] Cleanup failed: {e}")


async def main():
    """Main function for testing"""
    agent = OptionsAITrainingAgent()
    try:
        await agent.initialize()
        await agent.start()
    finally:
        await agent.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
#!/usr/bin/env python3
"""
Options AI Training Agent - Machine Learning Model Development

Features:
📊 1. Multi-Model Training
- LightGBM for gradient boosting
- SGD for incremental learning
- Random Forest for baseline

📈 2. Options-Specific Features
- Greeks-based feature engineering
- Volatility surface modeling
- Time decay prediction
- Multi-timeframe analysis

⚡ 3. Advanced Training
- Hyperparameter optimization
- Cross-validation strategies
- Feature importance analysis
- Model ensemble techniques

🎯 4. Performance Optimization
- GPU acceleration support
- Parallel training pipelines
- Memory-efficient processing
- Real-time model updates

🔄 5. Incremental Learning Support
- Online learning with partial_fit
- Concept drift detection
- Model versioning and rollback
- Continuous model updates
"""

import asyncio
import logging
import polars as pl
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import json
import joblib
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# Core ML libraries
import lightgbm as lgb
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import SGDRegressor, PassiveAggressiveRegressor
from sklearn.tree import DecisionTreeRegressor
import optuna

logger = logging.getLogger(__name__)

class OptionsAITrainingAgent:
    """
    Options AI Training Agent for ML model development
    
    Handles:
    - Options price prediction models
    - Strategy performance prediction
    - Risk assessment models
    - Model ensemble creation
    - Incremental learning support
    """
    
    def __init__(self, config_path: str = "config/options_ai_training_config.yaml"):
        """Initialize Options AI Training Agent"""
        self.config_path = config_path
        self.config = None
        self.is_running = False
        
        # Data paths
        self.data_path = Path("data")
        self.features_path = self.data_path / "features"
        self.models_path = self.data_path / "models"
        self.backtest_path = self.data_path / "backtest"
        
        # Create directories
        self.models_path.mkdir(parents=True, exist_ok=True)
        
        # Model storage
        self.trained_models = {}
        
        # Incremental learning support
        self.incremental_models = {}
        self.model_versions = {}
        
        logger.info("[INIT] Options AI Training Agent initialized")
    
    async def initialize(self, **kwargs):
        """Initialize the agent with optional parameters"""
        try:
            await self._load_config()
            
            # Store kwargs for later use
            self.init_kwargs = kwargs
            
            # Check for incremental training flag
            self.incremental_mode = kwargs.get('incremental', False)
            
            logger.info(f"[SUCCESS] Options AI Training Agent initialized successfully (incremental: {self.incremental_mode})")
            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize agent: {e}")
            return False
    
    async def _load_config(self):
        """Load configuration with incremental training support"""
        self.config = {
            'target_variables': ['option_price', 'implied_vol', 'strategy_return', 'annualized_return', 'sharpe_ratio'],
            'feature_columns': [
                # Greeks features
                'delta', 'gamma', 'theta', 'vega', 'iv_rank',
                # Index reference features
                'index_price', 'index_return_1min', 'index_return_3min', 'index_return_5min', 'index_return_15min',
                'index_volatility', 'index_volatility_10', 'index_volatility_50',
                'index_momentum_10', 'index_momentum_20', 'index_above_sma20', 'index_above_sma50',
                # Options-specific features
                'moneyness', 'moneyness_deviation', 'distance_from_atm', 'is_itm', 'time_to_expiry',
                # Combined features
                'delta_exposure', 'delta_pnl_1min', 'gamma_risk', 'vega_exposure', 'theta_decay_rate',
                # PE/CE analysis features
                'is_pe', 'is_ce', 'direction_alignment',
                # Technical features (timeframe-dependent)
                'rsi', 'sma_20', 'ema_20', 'volume_ratio', 'momentum'
            ],
            'model_types': ['lightgbm', 'sgd'],  # Added SGD for incremental learning
            'cv_folds': 3,
            'optuna_trials': 10,
            'optuna_timeout': 60,
            'use_index_reference': True,
            'pe_ce_filter': True,
            'timeframes': ['1min', '3min', '5min', '15min'],
            'train_ensemble': True,
            'parallel_training': True,
            'max_workers': 4,
            # Incremental learning settings
            'incremental_batch_size': 1000,
            'concept_drift_threshold': 0.1,
            'model_performance_window': 100,
            'incremental_models': ['sgd', 'passive_aggressive']
        }
    
    async def start(self, **kwargs) -> bool:
        """Start the AI training agent with incremental learning support"""
        try:
            logger.info("[START] Starting Options AI Training Agent...")
            
            # Check if incremental mode is enabled
            incremental = kwargs.get('incremental', False) or self.incremental_mode
            
            if incremental:
                logger.info("[INCREMENTAL] Starting incremental training mode...")
                return await self._start_incremental_training(**kwargs)
            else:
                logger.info("[BATCH] Starting batch training mode...")
                return await self._start_batch_training(**kwargs)
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to start agent: {e}")
            return False
    
    async def _start_incremental_training(self, **kwargs) -> bool:
        """Start incremental training workflow"""
        try:
            logger.info("[INCREMENTAL] Loading existing models...")
            
            # Load existing models if available
            await self._load_existing_models()
            
            # Load new training data
            training_data = await self._load_training_data()
            if training_data is None:
                return False
            
            # Process data in batches for incremental learning
            batch_size = self.config['incremental_batch_size']
            total_rows = training_data.height
            
            logger.info(f"[INCREMENTAL] Processing {total_rows} records in batches of {batch_size}")
            
            for i in range(0, total_rows, batch_size):
                batch_end = min(i + batch_size, total_rows)
                batch_data = training_data.slice(i, batch_end - i)
                
                logger.info(f"[INCREMENTAL] Processing batch {i//batch_size + 1}/{(total_rows + batch_size - 1)//batch_size}")
                
                # Update models incrementally
                await self._update_models_incrementally(batch_data)
                
                # Check for concept drift
                if await self._detect_concept_drift(batch_data):
                    logger.warning("[DRIFT] Concept drift detected, retraining models...")
                    await self._retrain_models_on_drift(batch_data)
            
            # Save updated models
            await self._save_models()
            
            logger.info("[SUCCESS] Incremental training completed")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Incremental training failed: {e}")
            return False
    
    async def _start_batch_training(self, **kwargs) -> bool:
        """Start traditional batch training workflow"""
        try:
            # Load training data
            training_data = await self._load_training_data()
            if training_data is None:
                return False
            
            # Train models for each target
            for target in self.config['target_variables']:
                if target not in training_data.columns:
                    continue
                    
                await self._train_target_models(training_data, target)
            
            # Save models
            await self._save_models()
            
            logger.info("[SUCCESS] Batch training completed")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Batch training failed: {e}")
            return False
    
    async def _load_training_data(self) -> Optional[pl.DataFrame]:
        """Load training data from feature engineering outputs"""
        try:
            logger.info("[LOAD] Loading training data...")

            # Load feature data from all timeframes
            timeframes = self.config['timeframes']
            underlyings = ['NIFTY', 'BANKNIFTY']
            all_training_data = []

            for timeframe in timeframes:
                timeframe_path = self.features_path / timeframe
                if not timeframe_path.exists():
                    continue

                for underlying in underlyings:
                    feature_files = list(timeframe_path.glob(f"{underlying}_{timeframe}_features_*.parquet"))
                    if feature_files:
                        latest_file = max(feature_files, key=lambda x: x.stat().st_mtime)
                        df = pl.read_parquet(latest_file)

                        # Filter for PE/CE options if enabled
                        if self.config['pe_ce_filter'] and 'option_type' in df.columns:
                            df = df.filter(
                                pl.col('option_type').is_not_null() &
                                pl.col('option_type').is_in(['PE', 'CE'])
                            )

                        if df.height > 0:
                            df = df.with_columns([pl.lit(timeframe).alias('timeframe')])
                            all_training_data.append(df)
                            logger.info(f"[LOAD] Loaded {df.height} records from {underlying} {timeframe}")

            if not all_training_data:
                logger.warning("[WARNING] No training data found")
                return None

            # Combine all data
            combined_data = pl.concat(all_training_data, how="diagonal")

            # Create target variables if missing
            combined_data = await self._create_target_variables(combined_data)

            logger.info(f"[SUCCESS] Loaded {combined_data.height} total training samples")
            return combined_data

        except Exception as e:
            logger.error(f"[ERROR] Failed to load training data: {e}")
            return None

    async def _create_target_variables(self, data: pl.DataFrame) -> pl.DataFrame:
        """Create target variables from available data"""
        try:
            # Create option_price target from close price
            if 'close' in data.columns and 'option_price' not in data.columns:
                data = data.with_columns([pl.col('close').alias('option_price')])

            # Create implied_vol target
            if 'implied_vol' not in data.columns:
                if 'close' in data.columns:
                    data = data.with_columns([
                        pl.col('close').pct_change().rolling_std(window_size=20).alias('implied_vol')
                    ])
                else:
                    data = data.with_columns([pl.lit(0.2).alias('implied_vol')])

            # Create strategy_return target
            if 'strategy_return' not in data.columns:
                if 'close' in data.columns:
                    data = data.with_columns([pl.col('close').pct_change().alias('strategy_return')])
                else:
                    data = data.with_columns([pl.lit(0.0).alias('strategy_return')])

            # Create annualized_return target
            if 'annualized_return' not in data.columns:
                data = data.with_columns([
                    (pl.col('strategy_return') * 252).alias('annualized_return')
                ])

            # Create sharpe_ratio target
            if 'sharpe_ratio' not in data.columns:
                data = data.with_columns([
                    (pl.col('strategy_return') / pl.col('implied_vol')).alias('sharpe_ratio')
                ])

            return data

        except Exception as e:
            logger.error(f"[ERROR] Failed to create target variables: {e}")
            return data

    async def _train_target_models(self, data: pl.DataFrame, target: str):
        """Train models for specific target"""
        try:
            logger.info(f"[TRAIN] Training models for target: {target}")

            if target not in data.columns:
                logger.warning(f"[WARNING] Target {target} not found in data")
                return

            # Feature selection
            feature_cols = [col for col in self.config['feature_columns'] if col in data.columns]
            if not feature_cols:
                logger.warning(f"[WARNING] No features found for target {target}")
                return

            # Convert to pandas for sklearn compatibility
            X_df = data.select(feature_cols).to_pandas()
            y_series = data.select([target]).to_pandas()[target]

            # Remove NaN values
            mask = ~(X_df.isna().any(axis=1) | y_series.isna())
            X = X_df[mask].values
            y = y_series[mask].values

            if len(X) == 0:
                logger.warning(f"[WARNING] No valid data for target {target}")
                return

            # Train models
            for model_type in self.config['model_types']:
                model_key = f"{target}_{model_type}"

                if model_type == 'lightgbm':
                    model = await self._train_lightgbm(X, y, model_key)
                elif model_type == 'sgd':
                    model = await self._train_sgd(X, y, model_key)

                if model:
                    self.trained_models[model_key] = {
                        'model': model,
                        'target': target,
                        'model_type': model_type,
                        'features': feature_cols
                    }
                    logger.info(f"[SUCCESS] Trained {model_key}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to train models for {target}: {e}")

    async def _train_lightgbm(self, X: np.ndarray, y: np.ndarray, model_key: str):
        """Train LightGBM model with Optuna optimization"""
        try:
            def objective(trial):
                params = {
                    'objective': 'regression',
                    'metric': 'rmse',
                    'boosting_type': 'gbdt',
                    'num_leaves': trial.suggest_int('num_leaves', 10, 300),
                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                    'feature_fraction': trial.suggest_float('feature_fraction', 0.4, 1.0),
                    'bagging_fraction': trial.suggest_float('bagging_fraction', 0.4, 1.0),
                    'bagging_freq': trial.suggest_int('bagging_freq', 1, 7),
                    'min_child_samples': trial.suggest_int('min_child_samples', 5, 100),
                    'verbosity': -1
                }

                # Time series cross-validation
                tscv = TimeSeriesSplit(n_splits=self.config['cv_folds'])
                scores = []

                for train_idx, val_idx in tscv.split(X):
                    X_train, X_val = X[train_idx], X[val_idx]
                    y_train, y_val = y[train_idx], y[val_idx]

                    train_data = lgb.Dataset(X_train, label=y_train)
                    val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)

                    model = lgb.train(
                        params,
                        train_data,
                        valid_sets=[val_data],
                        num_boost_round=100,  # Reduced for faster training
                        callbacks=[lgb.early_stopping(10), lgb.log_evaluation(0)]
                    )

                    y_pred = model.predict(X_val)
                    score = mean_squared_error(y_val, y_pred)
                    scores.append(score)

                return np.mean(scores)

            # Optimize hyperparameters
            study = optuna.create_study(direction='minimize')
            study.optimize(objective, n_trials=self.config['optuna_trials'], timeout=self.config['optuna_timeout'])

            # Train final model with best parameters
            best_params = study.best_params
            best_params.update({
                'objective': 'regression',
                'metric': 'rmse',
                'verbosity': -1
            })

            train_data = lgb.Dataset(X, label=y)
            final_model = lgb.train(best_params, train_data, num_boost_round=200)

            logger.info(f"[SUCCESS] LightGBM trained for {model_key}")
            return final_model

        except Exception as e:
            logger.error(f"[ERROR] LightGBM training failed for {model_key}: {e}")
            return None

    async def _train_sgd(self, X: np.ndarray, y: np.ndarray, model_key: str):
        """Train SGD model for incremental learning"""
        try:
            # Scale features for SGD
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)

            # Initialize SGD regressor
            model = SGDRegressor(
                loss='squared_error',
                learning_rate='adaptive',
                eta0=0.01,
                max_iter=1000,
                tol=1e-3,
                random_state=42
            )

            # Train model
            model.fit(X_scaled, y)

            # Store scaler with model
            model_with_scaler = {
                'model': model,
                'scaler': scaler
            }

            logger.info(f"[SUCCESS] SGD trained for {model_key}")
            return model_with_scaler

        except Exception as e:
            logger.error(f"[ERROR] SGD training failed for {model_key}: {e}")
            return None

    async def _load_existing_models(self):
        """Load existing models for incremental training"""
        try:
            model_files = list(self.models_path.glob("*.joblib"))
            for model_file in model_files:
                try:
                    model_data = joblib.load(model_file)
                    model_name = model_file.stem
                    self.trained_models[model_name] = model_data
                    logger.info(f"[LOAD] Loaded existing model: {model_name}")
                except Exception as e:
                    logger.warning(f"[WARNING] Failed to load model {model_file}: {e}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to load existing models: {e}")

    async def _update_models_incrementally(self, batch_data: pl.DataFrame):
        """Update models incrementally with new batch data"""
        try:
            for target in self.config['target_variables']:
                if target not in batch_data.columns:
                    continue

                # Get features
                feature_cols = [col for col in self.config['feature_columns'] if col in batch_data.columns]
                if not feature_cols:
                    continue

                # Prepare data
                X_df = batch_data.select(feature_cols).to_pandas()
                y_series = batch_data.select([target]).to_pandas()[target]

                mask = ~(X_df.isna().any(axis=1) | y_series.isna())
                X = X_df[mask].values
                y = y_series[mask].values

                if len(X) == 0:
                    continue

                # Update SGD models incrementally
                sgd_key = f"{target}_sgd"
                if sgd_key in self.trained_models:
                    model_data = self.trained_models[sgd_key]['model']
                    if isinstance(model_data, dict) and 'model' in model_data:
                        sgd_model = model_data['model']
                        scaler = model_data['scaler']

                        # Scale new data
                        X_scaled = scaler.transform(X)

                        # Partial fit
                        sgd_model.partial_fit(X_scaled, y)
                        logger.info(f"[INCREMENTAL] Updated {sgd_key} with {len(X)} samples")

        except Exception as e:
            logger.error(f"[ERROR] Failed to update models incrementally: {e}")

    async def _detect_concept_drift(self, batch_data: pl.DataFrame) -> bool:
        """Detect concept drift in new data"""
        try:
            # Simple concept drift detection based on performance degradation
            # In a real implementation, you would use more sophisticated methods
            return False  # Placeholder

        except Exception as e:
            logger.error(f"[ERROR] Failed to detect concept drift: {e}")
            return False

    async def _retrain_models_on_drift(self, batch_data: pl.DataFrame):
        """Retrain models when concept drift is detected"""
        try:
            logger.info("[DRIFT] Retraining models due to concept drift...")
            # Implement model retraining logic here

        except Exception as e:
            logger.error(f"[ERROR] Failed to retrain models on drift: {e}")

    async def _save_models(self):
        """Save trained models"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            for model_name, model_data in self.trained_models.items():
                model_file = self.models_path / f"{model_name}_{timestamp}.joblib"
                joblib.dump(model_data, model_file)
                logger.info(f"[SAVE] Saved model: {model_file}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to save models: {e}")

    async def cleanup(self):
        """Cleanup resources"""
        try:
            self.is_running = False
            logger.info("[CLEANUP] Options AI Training Agent cleaned up")
        except Exception as e:
            logger.error(f"[ERROR] Cleanup failed: {e}")


async def main():
    """Main function for testing"""
    agent = OptionsAITrainingAgent()
    try:
        await agent.initialize()
        await agent.start()
    finally:
        await agent.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
